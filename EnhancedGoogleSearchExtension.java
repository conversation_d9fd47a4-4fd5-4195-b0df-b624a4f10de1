package burp;

import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.awt.*;

/**
 * Enhanced Burp Suite Extension with multiple Google search options
 */
public class EnhancedGoogleSearchExtension implements IBurpExtender, IContextMenuFactory {
    
    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private PrintWriter stdout;
    private PrintWriter stderr;
    
    @Override
    public void registerExtenderCallbacks(IBurpExtenderCallbacks callbacks) {
        this.callbacks = callbacks;
        this.helpers = callbacks.getHelpers();
        this.stdout = new PrintWriter(callbacks.getStdout(), true);
        this.stderr = new PrintWriter(callbacks.getStderr(), true);
        
        callbacks.setExtensionName("Enhanced Google Search Extension");
        callbacks.registerContextMenuFactory(this);
        
        stdout.println("=== Enhanced Google Search Extension ===");
        stdout.println("Extension loaded with multiple search options!");
        stdout.println("========================================");
    }
    
    @Override
    public List<JMenuItem> createMenuItems(IContextMenuInvocation invocation) {
        List<JMenuItem> menuItems = new ArrayList<JMenuItem>();
        
        try {
            byte invocationContext = invocation.getInvocationContext();
            
            if (invocationContext == IContextMenuInvocation.CONTEXT_MESSAGE_EDITOR_REQUEST ||
                invocationContext == IContextMenuInvocation.CONTEXT_MESSAGE_VIEWER_REQUEST ||
                invocationContext == IContextMenuInvocation.CONTEXT_PROXY_HISTORY ||
                invocationContext == IContextMenuInvocation.CONTEXT_TARGET_SITE_MAP_TABLE ||
                invocationContext == IContextMenuInvocation.CONTEXT_TARGET_SITE_MAP_TREE) {
                
                IHttpRequestResponse[] messages = invocation.getSelectedMessages();
                if (messages != null && messages.length > 0) {
                    IHttpRequestResponse message = messages[0];
                    byte[] request = message.getRequest();
                    
                    if (request != null) {
                        String domain = extractDomainFromRequest(request);
                        if (domain != null && !domain.isEmpty()) {
                            
                            // 创建主菜单
                            JMenu mainMenu = new JMenu("Google Search for " + domain);
                            
                            // 添加多个搜索选项
                            JMenuItem adminSearch = new JMenuItem("Admin/Login Pages");
                            adminSearch.addActionListener(new SearchActionListener(domain, "admin"));
                            mainMenu.add(adminSearch);
                            
                            JMenuItem apiSearch = new JMenuItem("API Endpoints");
                            apiSearch.addActionListener(new SearchActionListener(domain, "api"));
                            mainMenu.add(apiSearch);
                            
                            JMenuItem configSearch = new JMenuItem("Config Files");
                            configSearch.addActionListener(new SearchActionListener(domain, "config"));
                            mainMenu.add(configSearch);
                            
                            JMenuItem backupSearch = new JMenuItem("Backup Files");
                            backupSearch.addActionListener(new SearchActionListener(domain, "backup"));
                            mainMenu.add(backupSearch);
                            
                            JMenuItem customSearch = new JMenuItem("Custom Search...");
                            customSearch.addActionListener(new CustomSearchActionListener(domain));
                            mainMenu.add(customSearch);
                            
                            menuItems.add(mainMenu);
                        }
                    }
                }
            }
        } catch (Exception e) {
            stderr.println("Error in createMenuItems: " + e.getMessage());
        }
        
        return menuItems;
    }
    
    private String extractDomainFromRequest(byte[] request) {
        try {
            IRequestInfo requestInfo = helpers.analyzeRequest(request);
            List<String> headers = requestInfo.getHeaders();
            
            for (String header : headers) {
                if (header.toLowerCase().startsWith("host:")) {
                    String hostHeader = header.substring(5).trim();
                    if (!hostHeader.isEmpty()) {
                        String domain = hostHeader.split(":")[0].trim();
                        if (isValidDomain(domain)) {
                            return domain;
                        }
                    }
                    break;
                }
            }
        } catch (Exception e) {
            stderr.println("Error extracting domain: " + e.getMessage());
        }
        return null;
    }
    
    private boolean isValidDomain(String domain) {
        if (domain == null || domain.isEmpty()) return false;
        return domain.contains(".") || domain.equals("localhost") || domain.matches("^[a-zA-Z0-9\\-]+$");
    }
    
    // 搜索动作监听器
    private class SearchActionListener implements ActionListener {
        private final String domain;
        private final String searchType;
        
        public SearchActionListener(String domain, String searchType) {
            this.domain = domain;
            this.searchType = searchType;
        }
        
        @Override
        public void actionPerformed(ActionEvent e) {
            String searchQuery = buildSearchQuery(domain, searchType);
            performGoogleSearch(searchQuery);
        }
        
        private String buildSearchQuery(String domain, String type) {
            switch (type) {
                case "admin":
                    return "site:" + domain + " inurl:login OR inurl:admin OR inurl:signin OR inurl:weadmin OR inurl:backoffice OR inurl:dashboard OR inurl:panel";
                case "api":
                    return "site:" + domain + " inurl:api OR inurl:rest OR inurl:graphql OR inurl:swagger OR inurl:openapi";
                case "config":
                    return "site:" + domain + " filetype:xml OR filetype:conf OR filetype:config OR filetype:ini OR filetype:yaml OR filetype:json";
                case "backup":
                    return "site:" + domain + " filetype:bak OR filetype:backup OR filetype:old OR filetype:tmp OR inurl:backup";
                default:
                    return "site:" + domain;
            }
        }
    }
    
    // 自定义搜索监听器
    private class CustomSearchActionListener implements ActionListener {
        private final String domain;
        
        public CustomSearchActionListener(String domain) {
            this.domain = domain;
        }
        
        @Override
        public void actionPerformed(ActionEvent e) {
            String customQuery = JOptionPane.showInputDialog(
                null,
                "Enter custom search terms for " + domain + ":",
                "Custom Google Search",
                JOptionPane.QUESTION_MESSAGE
            );
            
            if (customQuery != null && !customQuery.trim().isEmpty()) {
                String searchQuery = "site:" + domain + " " + customQuery.trim();
                performGoogleSearch(searchQuery);
            }
        }
    }
    
    private void performGoogleSearch(String searchQuery) {
        try {
            String encodedQuery = URLEncoder.encode(searchQuery, "UTF-8");
            String googleSearchUrl = "https://www.google.com/search?q=" + encodedQuery;
            
            stdout.println("Opening Google search: " + searchQuery);
            
            if (Desktop.isDesktopSupported() && Desktop.getDesktop().isSupported(Desktop.Action.BROWSE)) {
                Desktop.getDesktop().browse(new URI(googleSearchUrl));
            } else {
                String os = System.getProperty("os.name").toLowerCase();
                if (os.contains("win")) {
                    Runtime.getRuntime().exec("rundll32 url.dll,FileProtocolHandler " + googleSearchUrl);
                } else if (os.contains("mac")) {
                    Runtime.getRuntime().exec("open " + googleSearchUrl);
                } else if (os.contains("nix") || os.contains("nux")) {
                    Runtime.getRuntime().exec("xdg-open " + googleSearchUrl);
                }
            }
        } catch (Exception ex) {
            stderr.println("Error opening browser: " + ex.getMessage());
            JOptionPane.showMessageDialog(null, "Failed to open browser: " + ex.getMessage(), "Error", JOptionPane.ERROR_MESSAGE);
        }
    }
}
