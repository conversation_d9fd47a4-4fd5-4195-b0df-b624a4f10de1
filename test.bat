@echo off
echo Testing Google Search Extension Core Functionality
echo.

REM Check if Java is available
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java is not installed or not in PATH
    pause
    exit /b 1
)

REM Compile and run test
echo Compiling test class...
javac TestExtension.java

if %errorlevel% neq 0 (
    echo Error: Test compilation failed
    pause
    exit /b 1
)

echo Running tests...
echo.
java TestExtension

echo.
echo Cleaning up...
del TestExtension.class

pause
