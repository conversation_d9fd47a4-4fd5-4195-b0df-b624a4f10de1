package burp;

import java.util.List;

/**
 * This interface is used to retrieve key details about an HTTP request.
 */
public interface IRequestInfo {
    /**
     * This method is used to obtain the HTTP method used in the request.
     *
     * @return The HTTP method used in the request.
     */
    String getMethod();

    /**
     * This method is used to obtain the URL in the request.
     *
     * @return The URL in the request.
     */
    java.net.URL getUrl();

    /**
     * This method is used to obtain the HTTP headers contained in the request.
     *
     * @return The HTTP headers contained in the request.
     */
    List<String> getHeaders();

    /**
     * This method is used to obtain the offset within the request where the message body begins.
     *
     * @return The offset within the request where the message body begins.
     */
    int getBodyOffset();
}
