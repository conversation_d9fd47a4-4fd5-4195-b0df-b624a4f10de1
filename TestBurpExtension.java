package burp;

import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

/**
 * Simple test extension to verify context menu functionality
 */
public class TestBurpExtension implements IBurpExtender, IContextMenuFactory {
    
    private IBurpExtenderCallbacks callbacks;
    private PrintWriter stdout;
    private PrintWriter stderr;
    
    @Override
    public void registerExtenderCallbacks(IBurpExtenderCallbacks callbacks) {
        this.callbacks = callbacks;
        this.stdout = new PrintWriter(callbacks.getStdout(), true);
        this.stderr = new PrintWriter(callbacks.getStderr(), true);
        
        // Set extension name
        callbacks.setExtensionName("Test Context Menu Extension");
        
        // Register context menu factory
        callbacks.registerContextMenuFactory(this);
        
        // Log initialization
        stdout.println("=== TEST EXTENSION ===");
        stdout.println("Extension loaded successfully!");
        stdout.println("Context menu factory registered.");
        stdout.println("This extension should show a test menu item on ALL context menus.");
        stdout.println("======================");
    }
    
    @Override
    public List<JMenuItem> createMenuItems(IContextMenuInvocation invocation) {
        stdout.println("*** createMenuItems called! ***");
        
        List<JMenuItem> menuItems = new ArrayList<JMenuItem>();
        
        try {
            // Log context information
            byte context = invocation.getInvocationContext();
            stdout.println("Context: " + context);
            
            // Get selected messages
            IHttpRequestResponse[] messages = invocation.getSelectedMessages();
            if (messages != null) {
                stdout.println("Messages count: " + messages.length);
            } else {
                stdout.println("Messages: null");
            }
            
            // Always add a test menu item regardless of context
            JMenuItem testItem = new JMenuItem("TEST MENU ITEM - Context: " + context);
            testItem.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    stdout.println("Test menu item clicked! Context was: " + context);
                    JOptionPane.showMessageDialog(null, 
                        "Test menu item works!\nContext: " + context, 
                        "Test Success", 
                        JOptionPane.INFORMATION_MESSAGE);
                }
            });
            
            menuItems.add(testItem);
            stdout.println("Added test menu item.");
            
        } catch (Exception e) {
            stderr.println("Error in createMenuItems: " + e.getMessage());
            e.printStackTrace(stderr);
        }
        
        stdout.println("Returning " + menuItems.size() + " menu items.");
        return menuItems;
    }
}
