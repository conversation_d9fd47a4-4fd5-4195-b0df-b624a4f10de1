@echo off
echo Building Burp Suite Extension: Google Search Admin/Login Pages
echo.

REM Check if Java is available
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java is not installed or not in PATH
    echo Please install Java JDK 11 or later
    pause
    exit /b 1
)

REM Create output directory
if not exist "build" mkdir build

REM Download Burp Montoya API if not present
if not exist "montoya-api.jar" (
    echo Downloading Burp Montoya API...
    powershell -Command "Invoke-WebRequest -Uri 'https://portswigger.net/burp/releases/download?product=burpsuite&version=2023.10.3.7&type=jar' -OutFile 'montoya-api.jar'"
    if %errorlevel% neq 0 (
        echo Error: Failed to download Montoya API
        echo Please download montoya-api.jar manually from PortSwigger
        pause
        exit /b 1
    )
)

REM Compile the extension
echo Compiling extension...
javac -cp "montoya-api.jar" -d build GoogleSearchExtension.java

if %errorlevel% neq 0 (
    echo Error: Compilation failed
    pause
    exit /b 1
)

REM Create JAR file
echo Creating JAR file...
cd build
jar cf ../GoogleSearchExtension.jar burp/GoogleSearchExtension.class
cd ..

if %errorlevel% neq 0 (
    echo Error: JAR creation failed
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo Extension JAR file: GoogleSearchExtension.jar
echo.
echo To install:
echo 1. Open Burp Suite
echo 2. Go to Extensions tab
echo 3. Click "Add"
echo 4. Select "Java" as extension type
echo 5. Browse and select GoogleSearchExtension.jar
echo.
pause
