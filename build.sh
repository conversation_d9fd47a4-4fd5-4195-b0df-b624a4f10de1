#!/bin/bash

echo "Building Burp Suite Extension: Google Search Admin/Login Pages"
echo

# Check if Java is available
if ! command -v java &> /dev/null; then
    echo "Error: Java is not installed or not in PATH"
    echo "Please install Java JDK 11 or later"
    exit 1
fi

if ! command -v javac &> /dev/null; then
    echo "Error: java<PERSON> (Java compiler) is not installed or not in PATH"
    echo "Please install Java JDK 11 or later"
    exit 1
fi

# Create output directory
mkdir -p build

# Download Burp Montoya API if not present
if [ ! -f "montoya-api.jar" ]; then
    echo "Downloading Burp Montoya API..."
    if command -v curl &> /dev/null; then
        curl -L -o montoya-api.jar "https://portswigger.net/burp/releases/download?product=burpsuite&version=2023.10.3.7&type=jar"
    elif command -v wget &> /dev/null; then
        wget -O montoya-api.jar "https://portswigger.net/burp/releases/download?product=burpsuite&version=2023.10.3.7&type=jar"
    else
        echo "Error: Neither curl nor wget is available"
        echo "Please download montoya-api.jar manually from PortSwigger"
        exit 1
    fi
    
    if [ $? -ne 0 ]; then
        echo "Error: Failed to download Montoya API"
        echo "Please download montoya-api.jar manually from PortSwigger"
        exit 1
    fi
fi

# Compile the extension
echo "Compiling extension..."
javac -cp "montoya-api.jar" -d build GoogleSearchExtension.java

if [ $? -ne 0 ]; then
    echo "Error: Compilation failed"
    exit 1
fi

# Create JAR file
echo "Creating JAR file..."
cd build
jar cf ../GoogleSearchExtension.jar burp/GoogleSearchExtension.class
cd ..

if [ $? -ne 0 ]; then
    echo "Error: JAR creation failed"
    exit 1
fi

echo
echo "Build completed successfully!"
echo "Extension JAR file: GoogleSearchExtension.jar"
echo
echo "To install:"
echo "1. Open Burp Suite"
echo "2. Go to Extensions tab"
echo "3. Click \"Add\""
echo "4. Select \"Java\" as extension type"
echo "5. Browse and select GoogleSearchExtension.jar"
echo
