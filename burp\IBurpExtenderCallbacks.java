package burp;

import java.io.OutputStream;

/**
 * This interface is used by Burp Suite to pass to extensions a set of callback methods that 
 * can be used by extensions to perform various actions within Burp.
 */
public interface IBurpExtenderCallbacks {
    /**
     * This method is used to set the display name for the current extension, 
     * which will be displayed within the user interface for the Extender tool.
     *
     * @param name The extension name.
     */
    void setExtensionName(String name);

    /**
     * This method is used to obtain an IExtensionHelpers object, which can be used by 
     * the extension to perform numerous useful tasks.
     *
     * @return An object containing numerous helper methods, for tasks such as 
     * building and analyzing HTTP requests.
     */
    IExtensionHelpers getHelpers();

    /**
     * This method is used to obtain the current extension's standard output stream.
     *
     * @return The extension's standard output stream.
     */
    OutputStream getStdout();

    /**
     * This method is used to obtain the current extension's standard error stream.
     *
     * @return The extension's standard error stream.
     */
    OutputStream getStderr();

    /**
     * This method is used to register a factory for custom context menu items.
     *
     * @param factory An object created by the extension that implements the 
     * IContextMenuFactory interface.
     */
    void registerContextMenuFactory(IContextMenuFactory factory);
}
