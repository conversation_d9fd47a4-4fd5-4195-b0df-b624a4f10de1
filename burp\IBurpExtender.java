package burp;

/**
 * All extensions must implement this interface.
 * 
 * Implementations must be called BurpExtender, in the package burp, must be declared public, 
 * and must provide a default (public, no-argument) constructor.
 */
public interface IBurpExtender {
    /**
     * This method is invoked when the extension is loaded. It registers an instance of the
     * IBurpExtenderCallbacks interface, providing methods that may be invoked by the extension
     * to perform various actions.
     *
     * @param callbacks An IBurpExtenderCallbacks object.
     */
    void registerExtenderCallbacks(IBurpExtenderCallbacks callbacks);
}
