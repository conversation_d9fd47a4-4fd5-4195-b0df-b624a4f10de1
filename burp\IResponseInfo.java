package burp;

import java.util.List;

/**
 * This interface is used to retrieve key details about an HTTP response.
 */
public interface IResponseInfo {
    /**
     * This method is used to obtain the HTTP headers contained in the response.
     *
     * @return The HTTP headers contained in the response.
     */
    List<String> getHeaders();

    /**
     * This method is used to obtain the offset within the response where the message body begins.
     *
     * @return The offset within the response where the message body begins.
     */
    int getBodyOffset();

    /**
     * This method is used to obtain the HTTP status code contained in the response.
     *
     * @return The HTTP status code contained in the response.
     */
    short getStatusCode();
}
