package burp;

/**
 * This interface contains a number of helper methods, which extensions can use to 
 * assist with various common tasks that arise for Burp extensions.
 */
public interface IExtensionHelpers {
    /**
     * This method can be used to analyze an HTTP request, and obtain various key details 
     * about it.
     *
     * @param request An HTTP request.
     * @return An IRequestInfo object that can be queried to obtain details about the request.
     */
    IRequestInfo analyzeRequest(byte[] request);

    /**
     * This method can be used to analyze an HTTP request, and obtain various key details 
     * about it.
     *
     * @param httpService The HTTP service for the request.
     * @param request An HTTP request.
     * @return An IRequestInfo object that can be queried to obtain details about the request.
     */
    IRequestInfo analyzeRequest(IHttpService httpService, byte[] request);

    /**
     * This method can be used to analyze an HTTP response, and obtain various key details 
     * about it.
     *
     * @param response An HTTP response.
     * @return An IResponseInfo object that can be queried to obtain details about the response.
     */
    IResponseInfo analyzeResponse(byte[] response);
}
