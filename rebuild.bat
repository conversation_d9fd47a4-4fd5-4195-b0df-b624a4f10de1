@echo off
echo 重新编译Burp插件...
echo.

REM 设置Java路径
set JAVA_PATH="D:\Program Files\Java\jdk1.8.0_202\bin"

echo 1. 编译Java源文件...
%JAVA_PATH%\javac.exe -cp build -d build BurpGoogleSearchExtension.java

if %errorlevel% neq 0 (
    echo ❌ 编译失败！请检查代码语法。
    pause
    exit /b 1
)

echo ✅ 编译成功！

echo 2. 创建JAR文件...
cd build
%JAVA_PATH%\jar.exe cf ../BurpGoogleSearchExtension_Updated.jar burp/
cd ..

if %errorlevel% neq 0 (
    echo ❌ JAR创建失败！
    pause
    exit /b 1
)

echo ✅ JAR文件创建成功！

echo.
echo 🎉 插件重新构建完成！
echo 文件名: BurpGoogleSearchExtension_Updated.jar
echo.
echo 下一步:
echo 1. 在Burp Suite中卸载旧插件
echo 2. 安装新的 BurpGoogleSearchExtension_Updated.jar
echo 3. 测试新功能
echo.
pause
