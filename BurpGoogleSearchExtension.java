package burp;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Burp Suite Extension: Google Search for Admin/Login Pages
 * 
 * This extension adds a context menu item that extracts the domain from the Host header
 * and opens a Google search for admin/login pages on that domain.
 * 
 * Compatible with Burp Suite Classic API
 */
public class BurpGoogleSearchExtension implements IBurpExtender, IContextMenuFactory {
    
    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private PrintWriter stdout;
    private PrintWriter stderr;
    
    @Override
    public void registerExtenderCallbacks(IBurpExtenderCallbacks callbacks) {
        this.callbacks = callbacks;
        this.helpers = callbacks.getHelpers();
        this.stdout = new PrintWriter(callbacks.getStdout(), true);
        this.stderr = new PrintWriter(callbacks.getStderr(), true);

        // Set extension name
        callbacks.setExtensionName("Google Search Admin/Login Pages");

        // Register context menu factory
        callbacks.registerContextMenuFactory(this);

        // Log initialization with more details
        stdout.println("=== Google Search Extension ===");
        stdout.println("Extension loaded successfully!");
        stdout.println("Version: 1.0");
        stdout.println("Context menu factory registered.");
        stdout.println("Right-click on HTTP requests to access the Google search feature.");
        stdout.println("Supported contexts: Request Editor, Request Viewer, Proxy History, Target Site Map");
        stdout.println("================================");
    }
    
    @Override
    public List<JMenuItem> createMenuItems(IContextMenuInvocation invocation) {
        List<JMenuItem> menuItems = new ArrayList<JMenuItem>();

        try {
            // Log context menu invocation for debugging
            byte invocationContext = invocation.getInvocationContext();
            stdout.println("Context menu invoked. Context: " + invocationContext);

            // Only show menu item for HTTP requests/responses
            if (invocationContext == IContextMenuInvocation.CONTEXT_MESSAGE_EDITOR_REQUEST ||
                invocationContext == IContextMenuInvocation.CONTEXT_MESSAGE_VIEWER_REQUEST ||
                invocationContext == IContextMenuInvocation.CONTEXT_PROXY_HISTORY ||
                invocationContext == IContextMenuInvocation.CONTEXT_TARGET_SITE_MAP_TABLE ||
                invocationContext == IContextMenuInvocation.CONTEXT_TARGET_SITE_MAP_TREE) {

                stdout.println("Context is supported. Checking for selected messages...");

                IHttpRequestResponse[] messages = invocation.getSelectedMessages();
                if (messages != null && messages.length > 0) {
                    stdout.println("Found " + messages.length + " selected message(s).");

                    // Get the first selected message
                    IHttpRequestResponse message = messages[0];
                    byte[] request = message.getRequest();

                    if (request != null) {
                        stdout.println("Request data available. Extracting domain...");

                        // Extract domain from Host header
                        String domain = extractDomainFromRequest(request);
                        if (domain != null && !domain.isEmpty()) {
                            stdout.println("Domain extracted: " + domain);
                            JMenuItem menuItem = new JMenuItem("Google Search: Admin/Login Pages for " + domain);
                            menuItem.addActionListener(new GoogleSearchActionListener(domain));
                            menuItems.add(menuItem);
                            stdout.println("Menu item added successfully.");
                        } else {
                            stdout.println("No valid domain found in request.");
                        }
                    } else {
                        stdout.println("No request data available.");
                    }
                } else {
                    stdout.println("No messages selected or messages array is null.");
                }
            } else {
                stdout.println("Context not supported: " + invocationContext);
            }
        } catch (Exception e) {
            stderr.println("Error in createMenuItems: " + e.getMessage());
            e.printStackTrace(stderr);
        }

        stdout.println("Returning " + menuItems.size() + " menu item(s).");
        return menuItems;
    }
    
    /**
     * Extracts the domain from the Host header of an HTTP request
     */
    private String extractDomainFromRequest(byte[] request) {
        try {
            // Parse the request to get headers
            IRequestInfo requestInfo = helpers.analyzeRequest(request);
            List<String> headers = requestInfo.getHeaders();

            stdout.println("Analyzing request headers...");
            stdout.println("Total headers: " + headers.size());

            // Find the Host header
            for (String header : headers) {
                stdout.println("Header: " + header);

                if (header.toLowerCase().startsWith("host:")) {
                    String hostHeader = header.substring(5).trim();
                    stdout.println("Found Host header: '" + hostHeader + "'");

                    if (!hostHeader.isEmpty()) {
                        // Remove port number if present
                        String domain = hostHeader.split(":")[0].trim();
                        stdout.println("Extracted domain (before validation): '" + domain + "'");

                        // Basic validation - ensure it looks like a domain
                        if (isValidDomain(domain)) {
                            stdout.println("Domain validation passed: " + domain);
                            return domain;
                        } else {
                            stdout.println("Domain validation failed for: " + domain);
                        }
                    } else {
                        stdout.println("Host header is empty after trimming");
                    }
                    break;
                } else if (header.toLowerCase().contains("host")) {
                    stdout.println("Found header containing 'host' but not starting with 'host:': " + header);
                }
            }

            stdout.println("No valid Host header found");
        } catch (Exception e) {
            stderr.println("Error extracting domain: " + e.getMessage());
            e.printStackTrace(stderr);
        }

        return null;
    }
    
    /**
     * Basic domain validation - more permissive for testing
     */
    private boolean isValidDomain(String domain) {
        if (domain == null || domain.isEmpty()) {
            stdout.println("Domain validation failed: null or empty");
            return false;
        }

        // More permissive validation - allow localhost, IPs, and various domain formats
        if (domain.equals("localhost")) {
            stdout.println("Domain validation passed: localhost");
            return true;
        }

        // Allow IP addresses
        if (domain.matches("^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$")) {
            stdout.println("Domain validation passed: IP address");
            return true;
        }

        // Allow any string that looks like a domain (contains at least one dot or is localhost)
        if (domain.contains(".") || domain.equals("localhost")) {
            stdout.println("Domain validation passed: contains dot or is localhost");
            return true;
        }

        // For testing, also allow simple hostnames
        if (domain.matches("^[a-zA-Z0-9\\-]+$")) {
            stdout.println("Domain validation passed: simple hostname");
            return true;
        }

        stdout.println("Domain validation failed for: '" + domain + "'");
        return false;
    }
    
    /**
     * Action listener for the Google search menu item
     */
    private class GoogleSearchActionListener implements ActionListener {
        private final String domain;
        
        public GoogleSearchActionListener(String domain) {
            this.domain = domain;
        }
        
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                // Construct the Google search query
                String searchQuery = "site:" + domain + " inurl:login OR inurl:admin OR inurl:signin OR inurl:weadmin OR inurl:backoffice";
                
                // URL encode the search query (Java 8 compatible)
                String encodedQuery = URLEncoder.encode(searchQuery, "UTF-8");
                
                // Construct the Google search URL
                String googleSearchUrl = "https://www.google.com/search?q=" + encodedQuery;
                
                // Log the action
                stdout.println("Opening Google search for domain: " + domain);
                stdout.println("Search URL: " + googleSearchUrl);
                
                // Open the URL in the default browser
                openUrlInBrowser(googleSearchUrl);
                
                stdout.println("Successfully opened browser with Google search results.");
                
            } catch (Exception ex) {
                stderr.println("Error performing Google search: " + ex.getMessage());
                
                // Show error dialog to user
                SwingUtilities.invokeLater(new Runnable() {
                    @Override
                    public void run() {
                        JOptionPane.showMessageDialog(
                            null,
                            "Failed to open Google search: " + ex.getMessage(),
                            "Error",
                            JOptionPane.ERROR_MESSAGE
                        );
                    }
                });
            }
        }
        
        /**
         * Opens a URL in the default system browser
         */
        private void openUrlInBrowser(String url) throws IOException, URISyntaxException {
            if (Desktop.isDesktopSupported() && Desktop.getDesktop().isSupported(Desktop.Action.BROWSE)) {
                Desktop.getDesktop().browse(new URI(url));
            } else {
                // Fallback for systems without Desktop support
                String os = System.getProperty("os.name").toLowerCase();
                if (os.contains("win")) {
                    Runtime.getRuntime().exec("rundll32 url.dll,FileProtocolHandler " + url);
                } else if (os.contains("mac")) {
                    Runtime.getRuntime().exec("open " + url);
                } else if (os.contains("nix") || os.contains("nux")) {
                    Runtime.getRuntime().exec("xdg-open " + url);
                } else {
                    throw new IOException("Unsupported operating system for opening URLs");
                }
            }
        }
    }
}
