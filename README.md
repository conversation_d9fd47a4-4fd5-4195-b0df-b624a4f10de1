# Google Search Admin/Login Pages - Burp Suite Extension

这是一个Burp Suite扩展插件，可以从HTTP请求的Host头中提取域名，并在Google中搜索该域名的管理员/登录页面。

## 功能特性

- 在Burp Suite的右键菜单中添加"Google Search: Admin/Login Pages for [domain]"选项
- 自动从HTTP请求的Host头中提取域名
- 构造Google搜索查询：`site:{domain} inurl:login OR inurl:admin OR inurl:signin OR inurl:weadmin OR inurl:backoffice`
- 在默认浏览器中打开搜索结果

## 系统要求

- Burp Suite Professional 或 Community Edition
- Java JDK 11 或更高版本（注意：需要JDK，不仅仅是JRE）
- 支持的操作系统：Windows、Linux、macOS

### Java JDK 安装指南

#### Windows:
1. 访问 [Oracle JDK下载页面](https://www.oracle.com/java/technologies/downloads/) 或 [OpenJDK下载页面](https://adoptium.net/)
2. 下载适合您系统的JDK 11或更高版本
3. 运行安装程序并按照提示完成安装
4. 确保将JDK的bin目录添加到系统PATH环境变量中
5. 验证安装：打开命令提示符，运行 `javac -version`

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install openjdk-11-jdk
```

#### Linux (CentOS/RHEL):
```bash
sudo yum install java-11-openjdk-devel
```

#### macOS:
```bash
# 使用Homebrew
brew install openjdk@11

# 或者从Oracle/Adoptium下载安装包
```

## 安装步骤

### 方法1：使用构建脚本（推荐）

#### Windows:
```bash
# 运行构建脚本
build.bat
```

#### Linux/macOS:
```bash
# 给脚本执行权限
chmod +x build.sh

# 运行构建脚本
./build.sh
```

### 方法2：手动构建

1. 下载Burp Montoya API JAR文件
2. 编译Java源代码：
   ```bash
   javac -cp "montoya-api.jar" -d build GoogleSearchExtension.java
   ```
3. 创建JAR文件：
   ```bash
   cd build
   jar cf ../GoogleSearchExtension.jar burp/GoogleSearchExtension.class
   ```

### 在Burp Suite中安装

1. 打开Burp Suite
2. 转到"Extensions"标签页
3. 点击"Add"按钮
4. 选择"Java"作为扩展类型
5. 浏览并选择`GoogleSearchExtension.jar`文件
6. 点击"Next"完成安装

## 使用方法

1. 在Burp Suite中拦截或查看HTTP请求
2. 在Proxy、Repeater、或其他工具中右键点击请求
3. 选择"Google Search: Admin/Login Pages for [domain]"菜单项
4. 插件会自动：
   - 从Host头中提取域名
   - 构造Google搜索查询
   - 在默认浏览器中打开搜索结果

## 搜索查询格式

插件会构造以下格式的Google搜索查询：
```
site:{target_domain} inurl:login OR inurl:admin OR inurl:signin OR inurl:weadmin OR inurl:backoffice
```

例如，对于域名`example.com`，搜索查询将是：
```
site:example.com inurl:login OR inurl:admin OR inurl:signin OR inurl:weadmin OR inurl:backoffice
```

## 支持的URL路径

插件搜索包含以下关键词的URL路径：
- `login` - 登录页面
- `admin` - 管理员页面
- `signin` - 登录页面（另一种形式）
- `weadmin` - Web管理员页面
- `backoffice` - 后台管理页面

## 故障排除

### 编译错误
- 确保安装了Java JDK 11或更高版本
- 确保`javac`命令在PATH中可用
- 检查Montoya API JAR文件是否正确下载

### 插件加载失败
- 确保JAR文件正确创建
- 检查Burp Suite的错误日志
- 确保使用的是兼容的Burp Suite版本

### 浏览器无法打开
- 检查系统是否支持Desktop API
- 确保有默认浏览器设置
- 查看Burp Suite的输出日志获取详细错误信息

## 开发信息

- **语言**: Java
- **API**: Burp Suite Montoya API
- **兼容性**: Burp Suite 2023.x及更高版本

## 许可证

本项目采用MIT许可证。

## 贡献

欢迎提交问题报告和功能请求。如果您想贡献代码，请：

1. Fork本项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 安全注意事项

- 此插件会在外部浏览器中打开Google搜索
- 搜索查询可能会暴露目标域名信息
- 请在授权的渗透测试环境中使用

## 更新日志

### v1.0.0
- 初始版本
- 基本的域名提取和Google搜索功能
- 支持Windows、Linux、macOS
