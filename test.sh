#!/bin/bash

echo "Testing Google Search Extension Core Functionality"
echo

# Check if Java is available
if ! command -v java &> /dev/null; then
    echo "Error: Java is not installed or not in PATH"
    exit 1
fi

if ! command -v javac &> /dev/null; then
    echo "Error: j<PERSON><PERSON> (Java compiler) is not installed or not in PATH"
    exit 1
fi

# Compile and run test
echo "Compiling test class..."
javac TestExtension.java

if [ $? -ne 0 ]; then
    echo "Error: Test compilation failed"
    exit 1
fi

echo "Running tests..."
echo
java TestExtension

echo
echo "Cleaning up..."
rm -f TestExtension.class

echo "Test completed!"
