package burp;

import javax.swing.JMenuItem;
import java.util.List;

/**
 * Extensions can implement this interface and then call 
 * IBurpExtenderCallbacks.registerContextMenuFactory() to register a factory for 
 * custom context menu items.
 */
public interface IContextMenuFactory {
    /**
     * This method will be called by <PERSON><PERSON><PERSON> when the user invokes a context menu anywhere 
     * within Burp. The factory can then provide any custom context menu items that should 
     * be shown.
     *
     * @param invocation An object that implements the IContextMenuInvocation interface, 
     * which the extension can query to obtain details of the context menu invocation.
     * @return A list of custom menu items (which may include sub-menus, checkbox menu items, etc.) 
     * that should be displayed. Extensions may return null from this method, to indicate that 
     * no menu items are required.
     */
    List<JMenuItem> createMenuItems(IContextMenuInvocation invocation);
}
