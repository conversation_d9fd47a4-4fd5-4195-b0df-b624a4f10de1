package burp;

/**
 * This interface is used to retrieve and update details about HTTP messages that 
 * are being processed by Burp.
 */
public interface IHttpRequestResponse {
    /**
     * This method is used to retrieve the request relating to this object.
     *
     * @return The request relating to this object.
     */
    byte[] getRequest();

    /**
     * This method is used to update the request relating to this object.
     *
     * @param message The new request relating to this object.
     */
    void setRequest(byte[] message);

    /**
     * This method is used to retrieve the response relating to this object.
     *
     * @return The response relating to this object.
     */
    byte[] getResponse();

    /**
     * This method is used to update the response relating to this object.
     *
     * @param message The new response relating to this object.
     */
    void setResponse(byte[] message);

    /**
     * This method is used to retrieve the HTTP service for this object.
     *
     * @return The HTTP service for this object.
     */
    IHttpService getHttpService();

    /**
     * This method is used to update the HTTP service for this object.
     *
     * @param httpService The new HTTP service for this object.
     */
    void setHttpService(IHttpService httpService);
}
