@echo off
echo Burp Extension Modification and Build Script
echo ============================================
echo.

REM 设置Java路径
set JAVA_PATH="D:\Program Files\Java\jdk1.8.0_202\bin"

echo 1. 编译增强版插件...
%JAVA_PATH%\javac.exe -cp build -d build EnhancedGoogleSearchExtension.java

if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 2. 创建JAR文件...
cd build
%JAVA_PATH%\jar.exe cf ../EnhancedGoogleSearchExtension.jar burp/EnhancedGoogleSearchExtension.class
cd ..

if %errorlevel% neq 0 (
    echo JAR创建失败！
    pause
    exit /b 1
)

echo.
echo ✅ 增强版插件构建完成！
echo 文件: EnhancedGoogleSearchExtension.jar
echo.
echo 新功能:
echo - 多种搜索类型（Admin页面、API端点、配置文件、备份文件）
echo - 自定义搜索选项
echo - 子菜单组织
echo.
echo 安装步骤:
echo 1. 在Burp Suite中卸载旧插件
echo 2. 安装 EnhancedGoogleSearchExtension.jar
echo 3. 右键点击HTTP请求查看新的搜索选项
echo.
pause
