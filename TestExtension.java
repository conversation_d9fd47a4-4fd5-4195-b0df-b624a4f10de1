import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Simple test class to verify the core functionality of the Google Search Extension
 * without requiring Burp Suite to be running.
 */
public class TestExtension {
    
    public static void main(String[] args) {
        TestExtension test = new TestExtension();
        
        System.out.println("Testing Google Search Extension Core Functionality");
        System.out.println("================================================");
        
        // Test domain extraction
        test.testDomainValidation();
        
        // Test URL construction
        test.testUrlConstruction();
        
        System.out.println("\nAll tests completed!");
    }
    
    /**
     * Test domain validation logic
     */
    private void testDomainValidation() {
        System.out.println("\n1. Testing Domain Validation:");
        
        String[] testDomains = {
            "example.com",           // Valid
            "sub.example.com",       // Valid subdomain
            "test-site.org",         // Valid with hyphen
            "localhost",             // Invalid - no TLD
            "***********",          // Invalid - IP address
            "",                      // Invalid - empty
            "example.com:8080",      // Should be handled by port removal
            "very-long-domain-name-that-might-be-valid.com", // Valid long domain
            "invalid..domain.com",   // Invalid - double dots
            "example.c"              // Invalid - TLD too short
        };
        
        for (String domain : testDomains) {
            String cleanDomain = extractDomainFromHost(domain);
            boolean isValid = isValidDomain(cleanDomain);
            System.out.printf("  %-40s -> %-20s [%s]\n", 
                domain, 
                cleanDomain != null ? cleanDomain : "null", 
                isValid ? "VALID" : "INVALID"
            );
        }
    }
    
    /**
     * Test Google search URL construction
     */
    private void testUrlConstruction() {
        System.out.println("\n2. Testing URL Construction:");
        
        String[] testDomains = {
            "example.com",
            "test-site.org",
            "subdomain.example.net"
        };
        
        for (String domain : testDomains) {
            String searchUrl = constructGoogleSearchUrl(domain);
            System.out.printf("  Domain: %s\n", domain);
            System.out.printf("  URL:    %s\n\n", searchUrl);
        }
    }
    
    /**
     * Extract domain from host header (removes port if present)
     */
    private String extractDomainFromHost(String hostHeader) {
        if (hostHeader == null || hostHeader.isEmpty()) {
            return null;
        }
        
        // Remove port number if present
        return hostHeader.split(":")[0];
    }
    
    /**
     * Basic domain validation
     */
    private boolean isValidDomain(String domain) {
        if (domain == null || domain.isEmpty()) {
            return false;
        }
        
        // Basic regex for domain validation
        Pattern domainPattern = Pattern.compile("^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.[a-zA-Z]{2,}$");
        Matcher matcher = domainPattern.matcher(domain);
        return matcher.matches();
    }
    
    /**
     * Construct Google search URL for admin/login pages
     */
    private String constructGoogleSearchUrl(String domain) {
        try {
            // Construct the Google search query
            String searchQuery = "site:" + domain + " inurl:login OR inurl:admin OR inurl:signin OR inurl:weadmin OR inurl:backoffice";
            
            // URL encode the search query
            String encodedQuery = URLEncoder.encode(searchQuery, StandardCharsets.UTF_8);
            
            // Construct the Google search URL
            return "https://www.google.com/search?q=" + encodedQuery;
            
        } catch (Exception e) {
            return "Error constructing URL: " + e.getMessage();
        }
    }
}
