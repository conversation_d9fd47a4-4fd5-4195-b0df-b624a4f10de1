package burp;

/**
 * This interface is used when an extension registers an IContextMenuFactory. 
 * When the user requests a context menu, <PERSON><PERSON><PERSON> will call the factory's createMenuItems method 
 * and pass an object that implements this interface. This object can be queried by the 
 * extension to obtain details of the context menu invocation.
 */
public interface IContextMenuInvocation {
    /**
     * Used to indicate that the context menu is being invoked in a request editor.
     */
    static final byte CONTEXT_MESSAGE_EDITOR_REQUEST = 0;
    
    /**
     * Used to indicate that the context menu is being invoked in a response editor.
     */
    static final byte CONTEXT_MESSAGE_EDITOR_RESPONSE = 1;
    
    /**
     * Used to indicate that the context menu is being invoked in a non-editable request viewer.
     */
    static final byte CONTEXT_MESSAGE_VIEWER_REQUEST = 2;
    
    /**
     * Used to indicate that the context menu is being invoked in a non-editable response viewer.
     */
    static final byte CONTEXT_MESSAGE_VIEWER_RESPONSE = 3;
    
    /**
     * Used to indicate that the context menu is being invoked in the Proxy history.
     */
    static final byte CONTEXT_PROXY_HISTORY = 4;
    
    /**
     * Used to indicate that the context menu is being invoked in the Target site map table.
     */
    static final byte CONTEXT_TARGET_SITE_MAP_TABLE = 5;
    
    /**
     * Used to indicate that the context menu is being invoked in the Target site map tree.
     */
    static final byte CONTEXT_TARGET_SITE_MAP_TREE = 6;

    /**
     * This method can be used to retrieve the context within which the menu was invoked.
     *
     * @return An index indicating the context within which the menu was invoked. 
     * This will be one of the CONTEXT_* values defined within this interface.
     */
    byte getInvocationContext();

    /**
     * This method can be used to retrieve the HTTP requests/responses that were shown 
     * or selected by the user when the context menu was invoked.
     *
     * @return An array of IHttpRequestResponse objects representing the items that were 
     * shown or selected by the user when the context menu was invoked. This method returns 
     * null if no requests/responses were shown or selected.
     */
    IHttpRequestResponse[] getSelectedMessages();
}
