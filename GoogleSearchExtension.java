package burp;

import burp.api.montoya.BurpExtension;
import burp.api.montoya.MontoyaApi;
import burp.api.montoya.core.Registration;
import burp.api.montoya.http.message.HttpRequestResponse;
import burp.api.montoya.http.message.requests.HttpRequest;
import burp.api.montoya.ui.contextmenu.ContextMenuEvent;
import burp.api.montoya.ui.contextmenu.ContextMenuItemsProvider;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Burp Suite Extension: Google Search for Admin/Login Pages
 * 
 * This extension adds a context menu item that extracts the domain from the Host header
 * and opens a Google search for admin/login pages on that domain.
 */
public class GoogleSearchExtension implements BurpExtension, ContextMenuItemsProvider {
    
    private MontoyaApi api;
    private Registration registration;
    
    @Override
    public void initialize(MontoyaApi api) {
        this.api = api;
        
        // Set extension name
        api.extension().setName("Google Search Admin/Login Pages");
        
        // Register context menu provider
        registration = api.userInterface().registerContextMenuItemsProvider(this);
        
        // Log initialization
        api.logging().logToOutput("Google Search Extension loaded successfully!");
    }
    
    @Override
    public List<Component> provideMenuItems(ContextMenuEvent event) {
        List<Component> menuItems = new ArrayList<>();
        
        // Only show menu item if we have selected HTTP request/response
        Optional<HttpRequestResponse> requestResponse = event.selectedRequestResponse();
        if (requestResponse.isPresent()) {
            HttpRequest request = requestResponse.get().request();
            
            // Extract domain from Host header
            String domain = extractDomainFromRequest(request);
            if (domain != null && !domain.isEmpty()) {
                JMenuItem menuItem = new JMenuItem("Google Search: Admin/Login Pages for " + domain);
                menuItem.addActionListener(new GoogleSearchActionListener(domain));
                menuItems.add(menuItem);
            }
        }
        
        return menuItems;
    }
    
    /**
     * Extracts the domain from the Host header of an HTTP request
     */
    private String extractDomainFromRequest(HttpRequest request) {
        try {
            // Get the Host header value
            String hostHeader = request.headerValue("Host");
            if (hostHeader != null && !hostHeader.isEmpty()) {
                // Remove port number if present
                String domain = hostHeader.split(":")[0];
                
                // Basic validation - ensure it looks like a domain
                if (isValidDomain(domain)) {
                    return domain;
                }
            }
        } catch (Exception e) {
            api.logging().logToError("Error extracting domain: " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * Basic domain validation
     */
    private boolean isValidDomain(String domain) {
        if (domain == null || domain.isEmpty()) {
            return false;
        }
        
        // Basic regex for domain validation
        Pattern domainPattern = Pattern.compile("^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.[a-zA-Z]{2,}$");
        Matcher matcher = domainPattern.matcher(domain);
        return matcher.matches();
    }
    
    /**
     * Action listener for the Google search menu item
     */
    private class GoogleSearchActionListener implements ActionListener {
        private final String domain;
        
        public GoogleSearchActionListener(String domain) {
            this.domain = domain;
        }
        
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                // Construct the Google search query
                String searchQuery = "site:" + domain + " inurl:login OR inurl:admin OR inurl:signin OR inurl:weadmin OR inurl:backoffice";
                
                // URL encode the search query
                String encodedQuery = URLEncoder.encode(searchQuery, StandardCharsets.UTF_8);
                
                // Construct the Google search URL
                String googleSearchUrl = "https://www.google.com/search?q=" + encodedQuery;
                
                // Log the action
                api.logging().logToOutput("Opening Google search for domain: " + domain);
                api.logging().logToOutput("Search URL: " + googleSearchUrl);
                
                // Open the URL in the default browser
                openUrlInBrowser(googleSearchUrl);
                
            } catch (Exception ex) {
                api.logging().logToError("Error performing Google search: " + ex.getMessage());
                
                // Show error dialog to user
                SwingUtilities.invokeLater(() -> {
                    JOptionPane.showMessageDialog(
                        null,
                        "Failed to open Google search: " + ex.getMessage(),
                        "Error",
                        JOptionPane.ERROR_MESSAGE
                    );
                });
            }
        }
        
        /**
         * Opens a URL in the default system browser
         */
        private void openUrlInBrowser(String url) throws IOException, URISyntaxException {
            if (Desktop.isDesktopSupported() && Desktop.getDesktop().isSupported(Desktop.Action.BROWSE)) {
                Desktop.getDesktop().browse(new URI(url));
            } else {
                // Fallback for systems without Desktop support
                String os = System.getProperty("os.name").toLowerCase();
                if (os.contains("win")) {
                    Runtime.getRuntime().exec("rundll32 url.dll,FileProtocolHandler " + url);
                } else if (os.contains("mac")) {
                    Runtime.getRuntime().exec("open " + url);
                } else if (os.contains("nix") || os.contains("nux")) {
                    Runtime.getRuntime().exec("xdg-open " + url);
                } else {
                    throw new IOException("Unsupported operating system for opening URLs");
                }
            }
        }
    }
}
