package burp;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Burp Suite Extension: Google Search for Admin/Login Pages
 *
 * This extension adds a context menu item that extracts the domain from the Host header
 * and opens a Google search for admin/login pages on that domain.
 *
 * Compatible with classic Burp Suite API (Java 8+)
 */
public class GoogleSearchExtension implements IBurpExtender, IContextMenuFactory {

    private IBurpExtenderCallbacks callbacks;
    private IExtensionHelpers helpers;
    private PrintWriter stdout;
    private PrintWriter stderr;

    @Override
    public void registerExtenderCallbacks(IBurpExtenderCallbacks callbacks) {
        this.callbacks = callbacks;
        this.helpers = callbacks.getHelpers();
        this.stdout = new PrintWriter(callbacks.getStdout(), true);
        this.stderr = new PrintWriter(callbacks.getStderr(), true);

        // Set extension name
        callbacks.setExtensionName("Google Search Admin/Login Pages");

        // Register context menu factory
        callbacks.registerContextMenuFactory(this);

        // Log initialization
        stdout.println("Google Search Extension loaded successfully!");
    }
    
    @Override
    public List<JMenuItem> createMenuItems(IContextMenuInvocation invocation) {
        List<JMenuItem> menuItems = new ArrayList<>();

        // Only show menu item for HTTP requests/responses
        if (invocation.getInvocationContext() == IContextMenuInvocation.CONTEXT_MESSAGE_EDITOR_REQUEST ||
            invocation.getInvocationContext() == IContextMenuInvocation.CONTEXT_MESSAGE_VIEWER_REQUEST ||
            invocation.getInvocationContext() == IContextMenuInvocation.CONTEXT_PROXY_HISTORY ||
            invocation.getInvocationContext() == IContextMenuInvocation.CONTEXT_TARGET_SITE_MAP_TABLE) {

            IHttpRequestResponse[] messages = invocation.getSelectedMessages();
            if (messages != null && messages.length > 0) {
                // Get the first selected message
                IHttpRequestResponse message = messages[0];
                byte[] request = message.getRequest();

                if (request != null) {
                    // Extract domain from Host header
                    String domain = extractDomainFromRequest(request);
                    if (domain != null && !domain.isEmpty()) {
                        JMenuItem menuItem = new JMenuItem("Google Search: Admin/Login Pages for " + domain);
                        menuItem.addActionListener(new GoogleSearchActionListener(domain));
                        menuItems.add(menuItem);
                    }
                }
            }
        }

        return menuItems;
    }
    
    /**
     * Extracts the domain from the Host header of an HTTP request
     */
    private String extractDomainFromRequest(byte[] request) {
        try {
            // Parse the request to get headers
            IRequestInfo requestInfo = helpers.analyzeRequest(request);
            List<String> headers = requestInfo.getHeaders();

            // Find the Host header
            for (String header : headers) {
                if (header.toLowerCase().startsWith("host:")) {
                    String hostHeader = header.substring(5).trim();
                    if (!hostHeader.isEmpty()) {
                        // Remove port number if present
                        String domain = hostHeader.split(":")[0];

                        // Basic validation - ensure it looks like a domain
                        if (isValidDomain(domain)) {
                            return domain;
                        }
                    }
                    break;
                }
            }
        } catch (Exception e) {
            stderr.println("Error extracting domain: " + e.getMessage());
        }

        return null;
    }
    
    /**
     * Basic domain validation
     */
    private boolean isValidDomain(String domain) {
        if (domain == null || domain.isEmpty()) {
            return false;
        }
        
        // Basic regex for domain validation
        Pattern domainPattern = Pattern.compile("^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.[a-zA-Z]{2,}$");
        Matcher matcher = domainPattern.matcher(domain);
        return matcher.matches();
    }
    
    /**
     * Action listener for the Google search menu item
     */
    private class GoogleSearchActionListener implements ActionListener {
        private final String domain;

        public GoogleSearchActionListener(String domain) {
            this.domain = domain;
        }
        
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                // Construct the Google search query
                String searchQuery = "site:" + domain + " inurl:login OR inurl:admin OR inurl:signin OR inurl:weadmin OR inurl:backoffice";
                
                // URL encode the search query (Java 8 compatible)
                String encodedQuery = URLEncoder.encode(searchQuery, "UTF-8");

                // Construct the Google search URL
                String googleSearchUrl = "https://www.google.com/search?q=" + encodedQuery;

                // Log the action
                stdout.println("Opening Google search for domain: " + domain);
                stdout.println("Search URL: " + googleSearchUrl);
                
                // Open the URL in the default browser
                openUrlInBrowser(googleSearchUrl);
                
            } catch (Exception ex) {
                stderr.println("Error performing Google search: " + ex.getMessage());

                // Show error dialog to user
                SwingUtilities.invokeLater(new Runnable() {
                    @Override
                    public void run() {
                        JOptionPane.showMessageDialog(
                            null,
                            "Failed to open Google search: " + ex.getMessage(),
                            "Error",
                            JOptionPane.ERROR_MESSAGE
                        );
                    }
                });
            }
        }
        
        /**
         * Opens a URL in the default system browser
         */
        private void openUrlInBrowser(String url) throws IOException, URISyntaxException {
            if (Desktop.isDesktopSupported() && Desktop.getDesktop().isSupported(Desktop.Action.BROWSE)) {
                Desktop.getDesktop().browse(new URI(url));
            } else {
                // Fallback for systems without Desktop support
                String os = System.getProperty("os.name").toLowerCase();
                if (os.contains("win")) {
                    Runtime.getRuntime().exec("rundll32 url.dll,FileProtocolHandler " + url);
                } else if (os.contains("mac")) {
                    Runtime.getRuntime().exec("open " + url);
                } else if (os.contains("nix") || os.contains("nux")) {
                    Runtime.getRuntime().exec("xdg-open " + url);
                } else {
                    throw new IOException("Unsupported operating system for opening URLs");
                }
            }
        }
    }
}
