# 项目结构说明

这个Burp Suite扩展项目包含以下文件：

## 核心文件

### `GoogleSearchExtension.java`
- **描述**: 主要的扩展类，实现了Burp Suite插件的核心功能
- **功能**: 
  - 实现`BurpExtension`接口进行插件初始化
  - 实现`ContextMenuItemsProvider`接口提供右键菜单
  - 从HTTP请求的Host头中提取域名
  - 构造Google搜索URL并在浏览器中打开

## 构建文件

### `build.bat` (Windows)
- **描述**: Windows批处理脚本，用于编译和构建插件
- **功能**:
  - 检查Java环境
  - 下载Burp Montoya API（如果不存在）
  - 编译Java源代码
  - 创建JAR文件

### `build.sh` (Linux/macOS)
- **描述**: Unix shell脚本，用于编译和构建插件
- **功能**: 与`build.bat`相同，但适用于Unix系统

### `pom.xml` (Maven)
- **描述**: Maven项目配置文件
- **功能**:
  - 定义项目依赖（Burp Montoya API）
  - 配置编译参数
  - 设置JAR构建规则

### `build.gradle` (Gradle)
- **描述**: Gradle项目配置文件
- **功能**:
  - 替代Maven的构建配置
  - 包含自定义任务用于下载API
  - 提供灵活的构建选项

## 测试文件

### `TestExtension.java`
- **描述**: 独立的测试类，用于验证核心功能
- **功能**:
  - 测试域名验证逻辑
  - 测试URL构造功能
  - 不需要Burp Suite环境即可运行

### `test.bat` / `test.sh`
- **描述**: 测试脚本，编译并运行测试类
- **功能**:
  - 编译测试代码
  - 执行功能测试
  - 清理临时文件

## 文档文件

### `README.md`
- **描述**: 项目主要文档
- **内容**:
  - 功能介绍
  - 安装指南
  - 使用说明
  - 故障排除

### `PROJECT_STRUCTURE.md` (本文件)
- **描述**: 项目结构说明文档
- **内容**: 详细说明每个文件的用途和功能

## 构建产物

### `GoogleSearchExtension.jar`
- **描述**: 编译后的插件JAR文件
- **用途**: 在Burp Suite中安装的扩展文件
- **生成**: 通过构建脚本或构建工具生成

### `build/` 目录
- **描述**: 构建过程中的临时文件目录
- **内容**: 编译后的.class文件和其他构建产物

### `montoya-api.jar`
- **描述**: Burp Suite Montoya API的JAR文件
- **用途**: 编译时依赖
- **获取**: 通过构建脚本自动下载

## 使用流程

1. **开发环境准备**:
   - 安装Java JDK 11+
   - 确保`javac`和`jar`命令可用

2. **构建插件**:
   ```bash
   # Windows
   build.bat
   
   # Linux/macOS
   chmod +x build.sh
   ./build.sh
   
   # 或使用Maven
   mvn clean package
   
   # 或使用Gradle
   ./gradlew jar
   ```

3. **测试功能**:
   ```bash
   # Windows
   test.bat
   
   # Linux/macOS
   chmod +x test.sh
   ./test.sh
   ```

4. **安装到Burp Suite**:
   - 打开Burp Suite
   - Extensions → Add → Java → 选择`GoogleSearchExtension.jar`

## 依赖关系

```
GoogleSearchExtension.java
├── 编译时依赖: montoya-api.jar
├── 运行时环境: Burp Suite
└── 系统要求: Java 11+
```

## 开发注意事项

1. **API兼容性**: 插件使用Burp Suite Montoya API，确保与目标Burp版本兼容
2. **Java版本**: 最低要求Java 11，建议使用LTS版本
3. **权限要求**: 插件需要访问系统浏览器，可能需要相应权限
4. **跨平台**: 代码设计为跨平台兼容，支持Windows、Linux、macOS

## 扩展开发

如果需要扩展功能，可以考虑以下方向：

1. **搜索引擎扩展**: 支持其他搜索引擎（Bing、DuckDuckGo等）
2. **搜索词定制**: 允许用户自定义搜索关键词
3. **结果处理**: 直接在Burp中显示搜索结果
4. **历史记录**: 保存搜索历史和结果
5. **批量处理**: 支持批量域名搜索
