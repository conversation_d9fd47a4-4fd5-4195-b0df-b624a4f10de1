plugins {
    id 'java'
}

group = 'com.burp.extension'
version = '1.0.0'
description = 'Burp Suite extension to search for admin/login pages using Google'

java {
    sourceCompatibility = JavaVersion.VERSION_11
    targetCompatibility = JavaVersion.VERSION_11
}

repositories {
    mavenCentral()
}

dependencies {
    // Burp Suite Montoya API
    compileOnly 'net.portswigger.burp.extensions:montoya-api:2023.10.3.7'
}

sourceSets {
    main {
        java {
            srcDirs = ['.']
            include 'GoogleSearchExtension.java'
        }
    }
}

jar {
    archiveBaseName = 'GoogleSearchExtension'
    archiveVersion = ''
    archiveClassifier = ''
    
    from sourceSets.main.output
    
    manifest {
        attributes(
            'Implementation-Title': project.name,
            'Implementation-Version': project.version,
            'Implementation-Vendor': 'Burp Extension Developer'
        )
    }
}

// Task to download Montoya API if not available in Maven Central
task downloadMontoyaApi {
    doLast {
        def apiFile = file('montoya-api.jar')
        if (!apiFile.exists()) {
            println 'Downloading Burp Montoya API...'
            new URL('https://portswigger.net/burp/releases/download?product=burpsuite&version=2023.10.3.7&type=jar')
                .withInputStream { i -> apiFile.withOutputStream { it << i } }
        }
    }
}

// Custom task to compile with local JAR if needed
task compileWithLocalJar(type: JavaCompile) {
    dependsOn downloadMontoyaApi
    
    source = fileTree('.') {
        include 'GoogleSearchExtension.java'
    }
    
    classpath = files('montoya-api.jar')
    destinationDirectory = file('build/classes/java/main')
    
    options.encoding = 'UTF-8'
    sourceCompatibility = '11'
    targetCompatibility = '11'
}

// Custom JAR task using local compilation
task jarWithLocalApi(type: Jar) {
    dependsOn compileWithLocalJar
    
    archiveBaseName = 'GoogleSearchExtension'
    archiveVersion = ''
    
    from 'build/classes/java/main'
    
    manifest {
        attributes(
            'Implementation-Title': project.name,
            'Implementation-Version': project.version
        )
    }
}

// Clean task
clean {
    delete 'build'
    delete 'montoya-api.jar'
}

// Default build task
defaultTasks 'jar'

// Help task
task help {
    doLast {
        println """
Burp Suite Extension Build Script
=================================

Available tasks:
- jar                : Build extension JAR using Maven dependencies
- jarWithLocalApi    : Build extension JAR using locally downloaded API
- downloadMontoyaApi : Download Montoya API JAR file
- clean             : Clean build artifacts
- help              : Show this help message

Usage:
  ./gradlew jar                 # Build with Maven dependencies
  ./gradlew jarWithLocalApi     # Build with local API download
  ./gradlew clean               # Clean build files

Output: GoogleSearchExtension.jar
"""
    }
}
