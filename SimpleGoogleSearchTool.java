import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Simple Google Search Tool for Admin/Login Pages
 * 
 * This is a standalone version that demonstrates the core functionality
 * without requiring Burp Suite API dependencies.
 */
public class SimpleGoogleSearchTool extends JFrame {
    
    private JTextField domainField;
    private JButton searchButton;
    private JTextArea logArea;
    
    public SimpleGoogleSearchTool() {
        initializeGUI();
    }
    
    private void initializeGUI() {
        setTitle("Google Search Tool - Admin/Login Pages");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new BorderLayout());
        
        // Create input panel
        JPanel inputPanel = new JPanel(new FlowLayout());
        inputPanel.add(new JLabel("Domain:"));
        domainField = new JTextField(20);
        domainField.setText("example.com"); // Default example
        inputPanel.add(domainField);
        
        searchButton = new JButton("Search Admin/Login Pages");
        searchButton.addActionListener(new SearchActionListener());
        inputPanel.add(searchButton);
        
        add(inputPanel, BorderLayout.NORTH);
        
        // Create log area
        logArea = new JTextArea(10, 50);
        logArea.setEditable(false);
        logArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane scrollPane = new JScrollPane(logArea);
        scrollPane.setBorder(BorderFactory.createTitledBorder("Log"));
        add(scrollPane, BorderLayout.CENTER);
        
        // Create info panel
        JPanel infoPanel = new JPanel();
        infoPanel.setLayout(new BoxLayout(infoPanel, BoxLayout.Y_AXIS));
        infoPanel.setBorder(BorderFactory.createTitledBorder("Search Query"));
        
        JLabel queryLabel = new JLabel("<html><b>Query Format:</b><br>" +
            "site:{domain} inurl:login OR inurl:admin OR inurl:signin OR inurl:weadmin OR inurl:backoffice</html>");
        infoPanel.add(queryLabel);
        
        add(infoPanel, BorderLayout.SOUTH);
        
        pack();
        setLocationRelativeTo(null);
        
        // Allow Enter key to trigger search
        domainField.addActionListener(new SearchActionListener());
        
        log("Google Search Tool initialized successfully!");
        log("Enter a domain name and click 'Search Admin/Login Pages' to test the functionality.");
    }
    
    private void log(String message) {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                logArea.append("[" + new java.util.Date().toString() + "] " + message + "\n");
                logArea.setCaretPosition(logArea.getDocument().getLength());
            }
        });
    }
    
    /**
     * Extracts domain from host string (removes port if present)
     */
    private String extractDomainFromHost(String hostHeader) {
        if (hostHeader == null || hostHeader.isEmpty()) {
            return null;
        }
        
        // Remove port number if present
        return hostHeader.split(":")[0].trim();
    }
    
    /**
     * Basic domain validation
     */
    private boolean isValidDomain(String domain) {
        if (domain == null || domain.isEmpty()) {
            return false;
        }
        
        // Basic regex for domain validation
        Pattern domainPattern = Pattern.compile("^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.[a-zA-Z]{2,}$");
        Matcher matcher = domainPattern.matcher(domain);
        return matcher.matches();
    }
    
    /**
     * Construct Google search URL for admin/login pages
     */
    private String constructGoogleSearchUrl(String domain) {
        try {
            // Construct the Google search query
            String searchQuery = "site:" + domain + " inurl:login OR inurl:admin OR inurl:signin OR inurl:weadmin OR inurl:backoffice";
            
            // URL encode the search query (Java 8 compatible)
            String encodedQuery = URLEncoder.encode(searchQuery, "UTF-8");
            
            // Construct the Google search URL
            return "https://www.google.com/search?q=" + encodedQuery;
            
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * Opens a URL in the default system browser
     */
    private void openUrlInBrowser(String url) throws IOException, URISyntaxException {
        if (Desktop.isDesktopSupported() && Desktop.getDesktop().isSupported(Desktop.Action.BROWSE)) {
            Desktop.getDesktop().browse(new URI(url));
        } else {
            // Fallback for systems without Desktop support
            String os = System.getProperty("os.name").toLowerCase();
            if (os.contains("win")) {
                Runtime.getRuntime().exec("rundll32 url.dll,FileProtocolHandler " + url);
            } else if (os.contains("mac")) {
                Runtime.getRuntime().exec("open " + url);
            } else if (os.contains("nix") || os.contains("nux")) {
                Runtime.getRuntime().exec("xdg-open " + url);
            } else {
                throw new IOException("Unsupported operating system for opening URLs");
            }
        }
    }
    
    /**
     * Action listener for the search button
     */
    private class SearchActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String inputDomain = domainField.getText().trim();
            
            if (inputDomain.isEmpty()) {
                log("ERROR: Please enter a domain name");
                JOptionPane.showMessageDialog(SimpleGoogleSearchTool.this, 
                    "Please enter a domain name", "Input Required", JOptionPane.WARNING_MESSAGE);
                return;
            }
            
            // Extract clean domain (remove port if present)
            String domain = extractDomainFromHost(inputDomain);
            
            if (!isValidDomain(domain)) {
                log("ERROR: Invalid domain format: " + domain);
                JOptionPane.showMessageDialog(SimpleGoogleSearchTool.this, 
                    "Invalid domain format: " + domain, "Invalid Domain", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            try {
                // Construct search URL
                String searchUrl = constructGoogleSearchUrl(domain);
                if (searchUrl == null) {
                    throw new Exception("Failed to construct search URL");
                }
                
                log("Domain: " + domain);
                log("Search URL: " + searchUrl);
                log("Opening browser...");
                
                // Open URL in browser
                openUrlInBrowser(searchUrl);
                
                log("SUCCESS: Browser opened with Google search for domain: " + domain);
                
            } catch (Exception ex) {
                String errorMsg = "Failed to open Google search: " + ex.getMessage();
                log("ERROR: " + errorMsg);
                
                JOptionPane.showMessageDialog(SimpleGoogleSearchTool.this, 
                    errorMsg, "Error", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                new SimpleGoogleSearchTool().setVisible(true);
            }
        });
    }
}
